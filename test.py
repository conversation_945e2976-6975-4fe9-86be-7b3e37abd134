import pandas as pd

df = pd.read_excel('downloads/Bilxy ********.xlsx')
print('Records:', len(df))
print('Sample:')
for i in range(min(5, len(df))):
    print(f'{i+1}: {df.iloc[i]["Account No"]} -> {df.iloc[i]["Patient Name"]}')

manual = pd.read_excel('Bilxy ********.xlsx')
print(f'\nManual: {len(manual)} vs Automated: {len(df)}')
print('Manual sample:')
for i in range(min(3, len(manual))):
    print(f'{i+1}: {manual.iloc[i]["Account No"]} -> {manual.iloc[i]["Patient Name"]}')
