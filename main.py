import pdfplumber
import pandas as pd
import re
from datetime import datetime
import os
from typing import List, Dict, Any
import logging
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import tempfile
import shutil

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="PDF to Excel Converter", description="Convert Biloxi insurance PDF files to Excel format")

def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text from PDF file using pdfplumber
    """
    try:
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {e}")
        return ""

def parse_insurance_data(text: str) -> List[Dict[str, Any]]:
    """
    Parse the extracted text to extract insurance claim data
    Based on the exact format observed in the PDF
    """
    data = []
    lines = text.split('\n')

    current_account = None
    current_patient = None

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Skip header lines
        if any(header in line for header in ['Murphy Page:', 'Overdue Unpaid Insurance', 'Run:', 'Time:', 'Report Date', 'System:', 'Account Patient Name', 'Claim Last Dates']):
            continue

        # Check if this line contains a date (all data lines should have dates)
        date_pattern = r'(\d{2}/\d{2}/\d{2})'
        if not re.search(date_pattern, line):
            continue

        # Check if this line starts with an account number (new patient record)
        # Account numbers are typically 3+ letters followed by digits, optionally ending with X
        account_start_pattern = r'^([A-Z]{3,}\d+X?)'
        account_start_match = re.match(account_start_pattern, line)

        if account_start_match:
            # This is a new patient record line
            # Extract everything before the first date to get account + patient name
            date_match = re.search(date_pattern, line)
            before_date = line[:date_match.start()].strip()

            # Try pattern 1: Account with space (e.g., "HER8022X TINA ABSHIRE")
            spaced_pattern = r'^([A-Z]{3,}\d+X?)\s+([A-Z][A-Z\s\.]+)$'
            spaced_match = re.match(spaced_pattern, before_date)

            if spaced_match:
                current_account = spaced_match.group(1)
                current_patient = spaced_match.group(2).strip()
            else:
                # Pattern 2: Account concatenated with first name
                # Try different account length patterns
                concat_patterns = [
                    r'^([A-Z]{3}\d+X?)([A-Z][A-Z\s\.]+)$',      # 3 letters + digits
                    r'^([A-Z]{4}\d+X?)([A-Z][A-Z\s\.]+)$',      # 4 letters + digits
                    r'^([A-Z]{5}\d+X?)([A-Z][A-Z\s\.]+)$',      # 5 letters + digits
                    r'^([A-Z]{6}\d+X?)([A-Z][A-Z\s\.]+)$',      # 6 letters + digits
                    r'^([A-Z]{7}\d+X?)([A-Z][A-Z\s\.]+)$',      # 7 letters + digits
                    r'^([A-Z]{8}\d+X?)([A-Z][A-Z\s\.]+)$',      # 8 letters + digits
                    r'^([A-Z]{9}\d+X?)([A-Z][A-Z\s\.]+)$'       # 9 letters + digits
                ]

                matched = False
                for pattern in concat_patterns:
                    concat_match = re.match(pattern, before_date)
                    if concat_match:
                        potential_account = concat_match.group(1)
                        potential_patient = concat_match.group(2).strip()

                        # Validate that this looks like a reasonable split
                        if (re.search(r'\d+X?$', potential_account) and
                            re.match(r'^[A-Z]', potential_patient) and
                            len(potential_patient) > 1):  # Patient name should be reasonable length
                            current_account = potential_account
                            current_patient = potential_patient
                            matched = True
                            break

                if not matched:
                    # Fallback: use the account number we found and try to extract patient name
                    current_account = account_start_match.group(1)
                    remaining = before_date[len(current_account):].strip()
                    current_patient = remaining if remaining else "UNKNOWN"

            # Parse the claim data from this line
            claim_line = line[date_match.start():]
            parsed_data = parse_claim_line(claim_line, current_account, current_patient)
            if parsed_data:
                data.append(parsed_data)
        else:
            # This is a continuation line with just claim data (no account/patient info)
            if current_account and current_patient:
                parsed_data = parse_claim_line(line, current_account, current_patient)
                if parsed_data:
                    data.append(parsed_data)

    # Remove duplicates based on key fields
    unique_data = []
    seen_records = set()

    for record in data:
        # Create a tuple of key values to check for duplicates
        record_key = (
            record['Account No'],
            record['Patient Name'],
            record['DOS'],
            record['Insurance ID'],
            record['Claim Amount']
        )

        if record_key not in seen_records:
            unique_data.append(record)
            seen_records.add(record_key)

    logger.info(f"Removed {len(data) - len(unique_data)} duplicate records")
    return unique_data

def parse_claim_line(line: str, account: str, patient: str) -> Dict[str, Any]:
    """
    Parse a single claim line to extract claim information
    """
    try:
        # Pattern to match claim data
        # Example: 09/23/24 09/19/2409/19/24 BLUE CROSS OF MISSISSIPP Pri E 142.00 323YAX866187321M

        # Look for date patterns
        date_pattern = r'(\d{2}/\d{2}/\d{2})'
        dates = re.findall(date_pattern, line)

        if len(dates) < 1:
            return None

        # Use the second date as DOS if available (service date), otherwise first date
        dos_from = dates[1] if len(dates) > 1 else dates[0]

        # Extract insurance company name - more specific patterns
        insurance = "UNKNOWN"

        # Check for specific insurance patterns (order matters - check longer names first)
        if "BLUE CROSS OF MISSISSIPP" in line:
            insurance = "BLUE CROSS OF MISSISSIPP"
        elif "BLUE CROSS FEDERAL" in line:
            insurance = "BLUE CROSS FEDERAL"
        elif "BLUE CROSS STATE" in line:
            insurance = "BLUE CROSS STATE"
        elif "HUMANA" in line:
            insurance = "HUMANA"
        elif "AETNA" in line:
            insurance = "AETNA"
        elif "UNITED" in line:
            insurance = "UNITED"
        elif "CIGNA" in line:
            insurance = "CIGNA"
        elif "ANTHEM" in line:
            insurance = "ANTHEM"

        # Extract amount (look for decimal numbers, typically the first one is the claim amount)
        amount_pattern = r'(\d+\.\d{2})'
        amounts = re.findall(amount_pattern, line)
        claim_amount = float(amounts[0]) if amounts else 0.0

        # Extract overdue days and insurance ID from the end
        # Pattern: number followed by alphanumeric ID at end of line
        # Example: 323YAX866187321M or 281YAQ869242126M
        end_pattern = r'(\d+)([A-Z0-9]+[A-Z0-9M]?)$'
        end_match = re.search(end_pattern, line)

        if end_match:
            overdue_days = int(end_match.group(1))
            insurance_id = end_match.group(2)
        else:
            # Alternative pattern: just look for insurance ID at the end
            id_pattern = r'([A-Z0-9]{8,})$'
            id_match = re.search(id_pattern, line)
            insurance_id = id_match.group(1) if id_match else ""

            # Try to find overdue days before the insurance ID
            if insurance_id:
                overdue_pattern = r'(\d+)' + re.escape(insurance_id) + r'$'
                overdue_match = re.search(overdue_pattern, line)
                overdue_days = int(overdue_match.group(1)) if overdue_match else 0
            else:
                overdue_days = 0

        # Skip records with no insurance ID or invalid data
        if not insurance_id or claim_amount <= 0:
            return None

        # Convert date format from MM/DD/YY to YYYY-MM-DD
        dos_formatted = convert_date_format(dos_from)

        return {
            'Account No': account,
            'Patient Name': patient,
            'Insurance': insurance,
            'DOS': dos_formatted,
            'Insurance ID': insurance_id,
            'Claim Amount': claim_amount,
            'Over Due': overdue_days
        }

    except Exception as e:
        logger.warning(f"Error parsing line: {line[:50]}... Error: {e}")
        return None

def convert_date_format(date_str: str) -> str:
    """
    Convert date from MM/DD/YY to YYYY-MM-DD format
    """
    try:
        # Parse MM/DD/YY format
        date_obj = datetime.strptime(date_str, '%m/%d/%y')
        # Return in YYYY-MM-DD format
        return date_obj.strftime('%Y-%m-%d')
    except:
        return date_str

def create_excel_file(data: List[Dict[str, Any]], output_filename: str) -> bool:
    """
    Create Excel file from parsed data
    """
    try:
        if not data:
            logger.warning("No data to write to Excel file")
            return False

        df = pd.DataFrame(data)

        # Ensure columns are in the correct order
        column_order = ['Account No', 'Patient Name', 'Insurance', 'DOS', 'Insurance ID', 'Claim Amount', 'Over Due']
        df = df.reindex(columns=column_order)

        # Save to Excel
        df.to_excel(output_filename, index=False)
        logger.info(f"Excel file created successfully: {output_filename}")
        logger.info(f"Total records: {len(df)}")

        return True

    except Exception as e:
        logger.error(f"Error creating Excel file: {e}")
        return False

def find_pdf_files() -> List[str]:
    """
    Find all PDF files in the current directory that match the Biloxi pattern
    """
    pdf_files = []
    for file in os.listdir('.'):
        if file.lower().endswith('.pdf') and 'biloxi' in file.lower():
            pdf_files.append(file)
    return pdf_files

def convert_pdf_to_excel(pdf_filename: str, output_filename: str = None) -> bool:
    """
    Convert a single PDF file to Excel format
    """
    if output_filename is None:
        # Generate output filename based on PDF filename
        base_name = os.path.splitext(pdf_filename)[0]
        # Replace "Biloxi" with "Bilxy" to match your naming convention
        base_name = base_name.replace("Biloxi", "Bilxy").replace("biloxi", "Bilxy")
        output_filename = f"{base_name}.xlsx"

    logger.info("Starting PDF to Excel conversion...")
    logger.info(f"Input PDF: {pdf_filename}")
    logger.info(f"Output Excel: {output_filename}")

    # Check if PDF file exists
    if not os.path.exists(pdf_filename):
        logger.error(f"PDF file not found: {pdf_filename}")
        return False

    # Step 1: Extract text from PDF
    logger.info("Extracting text from PDF...")
    text = extract_text_from_pdf(pdf_filename)

    if not text:
        logger.error("Failed to extract text from PDF")
        return False

    logger.info(f"Extracted {len(text)} characters from PDF")

    # Step 2: Parse insurance data
    logger.info("Parsing insurance data...")
    data = parse_insurance_data(text)

    if not data:
        logger.error("No data could be parsed from PDF")
        return False

    logger.info(f"Parsed {len(data)} records")

    # Step 3: Create Excel file
    logger.info("Creating Excel file...")
    success = create_excel_file(data, output_filename)

    if success:
        logger.info("PDF to Excel conversion completed successfully!")
        return True
    else:
        logger.error("Failed to create Excel file")
        return False

def main():
    """
    Main function to convert PDF to Excel
    """
    import sys

    if len(sys.argv) > 1:
        # PDF filename provided as command line argument
        pdf_filename = sys.argv[1]
        output_filename = sys.argv[2] if len(sys.argv) > 2 else None
        return convert_pdf_to_excel(pdf_filename, output_filename)
    else:
        # Auto-detect PDF files in current directory
        pdf_files = find_pdf_files()

        if not pdf_files:
            logger.error("No Biloxi PDF files found in current directory")
            logger.info("Usage: python main.py [pdf_filename] [output_filename]")
            return False

        if len(pdf_files) == 1:
            # Only one PDF file found, process it
            return convert_pdf_to_excel(pdf_files[0])
        else:
            # Multiple PDF files found, let user choose or process all
            logger.info(f"Found {len(pdf_files)} PDF files:")
            for i, file in enumerate(pdf_files, 1):
                logger.info(f"{i}. {file}")

            # For now, process the first one (you can modify this behavior)
            logger.info(f"Processing the first file: {pdf_files[0]}")
            return convert_pdf_to_excel(pdf_files[0])

# Web interface routes
@app.get("/", response_class=HTMLResponse)
async def upload_form():
    """
    Serve the HTML upload form
    """
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PDF to Excel Converter</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .upload-area {
                border: 2px dashed #ccc;
                border-radius: 10px;
                padding: 40px;
                text-align: center;
                margin: 20px 0;
                background-color: #fafafa;
                transition: border-color 0.3s ease;
            }
            .upload-area:hover {
                border-color: #007bff;
            }
            .upload-area.dragover {
                border-color: #007bff;
                background-color: #e3f2fd;
            }
            input[type="file"] {
                display: none;
            }
            .upload-btn {
                background-color: #007bff;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin: 10px;
            }
            .upload-btn:hover {
                background-color: #0056b3;
            }
            .convert-btn {
                background-color: #28a745;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin: 10px;
                display: none;
            }
            .convert-btn:hover {
                background-color: #218838;
            }
            .file-info {
                margin: 20px 0;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 5px;
                display: none;
            }
            .progress {
                width: 100%;
                height: 20px;
                background-color: #e9ecef;
                border-radius: 10px;
                overflow: hidden;
                margin: 20px 0;
                display: none;
            }
            .progress-bar {
                height: 100%;
                background-color: #007bff;
                width: 0%;
                transition: width 0.3s ease;
            }
            .result {
                margin: 20px 0;
                padding: 15px;
                border-radius: 5px;
                display: none;
            }
            .success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            .error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            .download-btn {
                background-color: #17a2b8;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                text-decoration: none;
                display: inline-block;
                margin: 10px 0;
            }
            .download-btn:hover {
                background-color: #138496;
                text-decoration: none;
                color: white;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>PDF to Excel Converter</h1>
            <p style="text-align: center; color: #666;">
                Upload your Biloxi insurance PDF file and convert it to Excel format
            </p>

            <form id="uploadForm" enctype="multipart/form-data">
                <div class="upload-area" id="uploadArea">
                    <p>Drag and drop your PDF file here or</p>
                    <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose File
                    </button>
                    <input type="file" id="fileInput" name="file" accept=".pdf" onchange="handleFileSelect(event)">
                </div>

                <div class="file-info" id="fileInfo">
                    <strong>Selected file:</strong> <span id="fileName"></span><br>
                    <strong>Size:</strong> <span id="fileSize"></span>
                </div>

                <div style="text-align: center;">
                    <button type="button" class="convert-btn" id="convertBtn" onclick="convertFile()">
                        Convert to Excel
                    </button>
                </div>
            </form>

            <div class="progress" id="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>

            <div class="result" id="result"></div>
        </div>

        <script>
            let selectedFile = null;

            // Handle file selection
            function handleFileSelect(event) {
                const file = event.target.files[0];
                if (file) {
                    selectedFile = file;
                    document.getElementById('fileName').textContent = file.name;
                    document.getElementById('fileSize').textContent = formatFileSize(file.size);
                    document.getElementById('fileInfo').style.display = 'block';
                    document.getElementById('convertBtn').style.display = 'inline-block';
                }
            }

            // Format file size
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Handle drag and drop
            const uploadArea = document.getElementById('uploadArea');

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type === 'application/pdf') {
                        selectedFile = file;
                        document.getElementById('fileName').textContent = file.name;
                        document.getElementById('fileSize').textContent = formatFileSize(file.size);
                        document.getElementById('fileInfo').style.display = 'block';
                        document.getElementById('convertBtn').style.display = 'inline-block';
                    } else {
                        showResult('Please select a PDF file.', 'error');
                    }
                }
            });

            // Convert file
            async function convertFile() {
                if (!selectedFile) {
                    showResult('Please select a file first.', 'error');
                    return;
                }

                const formData = new FormData();
                formData.append('file', selectedFile);

                // Show progress
                document.getElementById('progress').style.display = 'block';
                document.getElementById('convertBtn').disabled = true;
                document.getElementById('result').style.display = 'none';

                // Simulate progress
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress > 90) progress = 90;
                    document.getElementById('progressBar').style.width = progress + '%';
                }, 200);

                try {
                    const response = await fetch('/convert', {
                        method: 'POST',
                        body: formData
                    });

                    clearInterval(progressInterval);
                    document.getElementById('progressBar').style.width = '100%';

                    if (response.ok) {
                        const result = await response.json();
                        showResult(
                            `Conversion successful! Processed ${result.records_count} records.
                            <a href="/download/${result.filename}" class="download-btn">Download Excel File</a>`,
                            'success'
                        );
                    } else {
                        const error = await response.json();
                        showResult(`Error: ${error.detail}`, 'error');
                    }
                } catch (error) {
                    clearInterval(progressInterval);
                    showResult(`Error: ${error.message}`, 'error');
                } finally {
                    document.getElementById('convertBtn').disabled = false;
                    setTimeout(() => {
                        document.getElementById('progress').style.display = 'none';
                        document.getElementById('progressBar').style.width = '0%';
                    }, 1000);
                }
            }

            // Show result message
            function showResult(message, type) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = message;
                resultDiv.className = `result ${type}`;
                resultDiv.style.display = 'block';
            }
        </script>
    </body>
    </html>
    """
    return html_content

@app.post("/convert")
async def convert_pdf(file: UploadFile = File(...)):
    """
    Convert uploaded PDF to Excel
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="Please upload a PDF file")

        # Create temporary file for the uploaded PDF
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:
            # Copy uploaded file content to temporary file
            shutil.copyfileobj(file.file, temp_pdf)
            temp_pdf_path = temp_pdf.name

        try:
            # Generate output filename
            base_name = os.path.splitext(file.filename)[0]
            base_name = base_name.replace("Biloxi", "Bilxy").replace("biloxi", "Bilxy")
            output_filename = f"{base_name}.xlsx"
            output_path = os.path.join("downloads", output_filename)

            # Create downloads directory if it doesn't exist
            os.makedirs("downloads", exist_ok=True)

            # Extract text from PDF
            text = extract_text_from_pdf(temp_pdf_path)
            if not text:
                raise HTTPException(status_code=400, detail="Could not extract text from PDF")

            # Parse insurance data
            data = parse_insurance_data(text)
            if not data:
                raise HTTPException(status_code=400, detail="No insurance data found in PDF")

            # Create Excel file
            success = create_excel_file(data, output_path)
            if not success:
                raise HTTPException(status_code=500, detail="Failed to create Excel file")

            return {
                "message": "Conversion successful",
                "filename": output_filename,
                "records_count": len(data)
            }

        finally:
            # Clean up temporary PDF file
            if os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during conversion: {e}")
        raise HTTPException(status_code=500, detail=f"Conversion failed: {str(e)}")

@app.get("/download/{filename}")
async def download_file(filename: str):
    """
    Download the converted Excel file
    """
    file_path = os.path.join("downloads", filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def run_web_server():
    """
    Run the web server
    """
    logger.info("Starting web server...")
    logger.info("Open your browser and go to: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "web":
        # Run web server
        run_web_server()
    elif len(sys.argv) > 1:
        # Command line mode - PDF filename provided as argument
        pdf_filename = sys.argv[1]
        output_filename = sys.argv[2] if len(sys.argv) > 2 else None
        convert_pdf_to_excel(pdf_filename, output_filename)
    else:
        # Default: run web server
        run_web_server()